<?php
/**
 * Professional Homepage - Rebuilt from Scratch
 * Modern Arabic RTL E-commerce Homepage
 *
 * @version 3.0
 * <AUTHOR> Development Team
 * @description Complete homepage rebuild with professional design,
 *              error-free implementation, and modern e-commerce best practices
 */

// Set page title and include header
$pageTitle = 'الرئيسية';
require_once 'includes/header.php';

// Initialize homepage settings table if not exists
try {
    $tableExists = fetchOne("SHOW TABLES LIKE 'homepage_settings'");
    if (!$tableExists) {
        // Create homepage settings table
        $createTableQuery = "
            CREATE TABLE `homepage_settings` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `section_name` varchar(100) NOT NULL,
                `setting_key` varchar(100) NOT NULL,
                `setting_value` text,
                `setting_type` enum('text','textarea','image','url','number','boolean') DEFAULT 'text',
                `sort_order` int(11) DEFAULT 0,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_section_key` (`section_name`,`setting_key`),
                KEY `idx_section` (`section_name`),
                KEY `idx_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $pdo->exec($createTableQuery);

        // Insert default homepage settings
        $defaultSettings = [
            // Hero Carousel Settings
            ['carousel', 'slide_1_image', 'assets/images/carousel-slide-1.svg', 'image', 1],
            ['carousel', 'slide_1_title', 'مرحباً بك في متجر Care الإلكتروني', 'text', 2],
            ['carousel', 'slide_1_subtitle', 'اكتشف مجموعة واسعة من المنتجات عالية الجودة بأفضل الأسعار في العراق', 'text', 3],
            ['carousel', 'slide_1_button_text', 'تصفح المنتجات', 'text', 4],
            ['carousel', 'slide_1_button_url', '/products.php', 'url', 5],
            ['carousel', 'slide_2_image', 'assets/images/carousel-slide-2.svg', 'image', 6],
            ['carousel', 'slide_2_title', 'عروض حصرية لفترة محدودة', 'text', 7],
            ['carousel', 'slide_2_subtitle', 'خصومات تصل إلى 50% على مجموعة مختارة من المنتجات', 'text', 8],
            ['carousel', 'slide_3_image', 'assets/images/carousel-slide-3.svg', 'image', 9],
            ['carousel', 'slide_3_title', 'توصيل مجاني لجميع أنحاء العراق', 'text', 10],
            ['carousel', 'slide_3_subtitle', 'استمتع بالتوصيل المجاني للطلبات أكثر من 200 ألف دينار', 'text', 11],
            ['carousel', 'auto_advance_time', '8', 'number', 12],
            ['carousel', 'show_indicators', '1', 'boolean', 13],
            ['carousel', 'show_controls', '1', 'boolean', 14],

            // Featured Products Settings
            ['featured_products', 'show_section', '1', 'boolean', 1],
            ['featured_products', 'section_title', 'المنتجات المميزة', 'text', 2],
            ['featured_products', 'section_subtitle', 'اكتشف أفضل منتجاتنا المختارة بعناية لتلبية احتياجاتك', 'text', 3],
            ['featured_products', 'products_limit', '8', 'number', 4],

            // Special Offers Settings
            ['offers', 'show_section', '1', 'boolean', 1],
            ['offers', 'section_title', 'العروض الخاصة', 'text', 2],
            ['offers', 'section_subtitle', 'لا تفوت عروضنا المحدودة والحصرية - وفر أكثر مع خصوماتنا المميزة', 'text', 3],
            ['offers', 'banner_image', 'assets/images/offers-banner.svg', 'image', 4],
            ['offers', 'banner_title', 'خصومات تصل إلى 50%', 'text', 5],
            ['offers', 'banner_subtitle', 'على مجموعة مختارة من أفضل المنتجات', 'text', 6],
            ['offers', 'banner_button_text', 'تصفح العروض', 'text', 7],
            ['offers', 'banner_button_url', '/offers.php', 'url', 8]
        ];

        $insertQuery = "INSERT INTO homepage_settings (section_name, setting_key, setting_value, setting_type, sort_order) VALUES (?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($insertQuery);

        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
    }
} catch (Exception $e) {
    error_log("Homepage settings initialization error: " . $e->getMessage());
}

// Enhanced helper function for fetching homepage section settings
if (!function_exists('getHomepageSectionSettings')) {
    function getHomepageSectionSettings($section) {
        try {
            global $pdo;

            // Validate database connection
            if (!$pdo) {
                error_log("Database connection not available in getHomepageSectionSettings");
                return [];
            }

            // Check if table exists
            $tableCheck = $pdo->query("SHOW TABLES LIKE 'homepage_settings'");
            if (!$tableCheck->fetch()) {
                error_log("homepage_settings table does not exist");
                return [];
            }

            // Fetch section settings with proper error handling
            $query = "SELECT setting_key, setting_value FROM homepage_settings WHERE section_name = ? AND is_active = 1 ORDER BY sort_order ASC";
            $stmt = $pdo->prepare($query);
            $stmt->execute([$section]);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $settings = [];
            foreach ($results as $row) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }

            return $settings;
        } catch (PDOException $e) {
            error_log("Database error in getHomepageSectionSettings: " . $e->getMessage());
            return [];
        } catch (Exception $e) {
            error_log("General error in getHomepageSectionSettings: " . $e->getMessage());
            return [];
        }
    }
}

// Enhanced helper function for safe Arabic date formatting
if (!function_exists('formatArabicDate')) {
    function formatArabicDate($date) {
        try {
            if (empty($date)) return '';
            $timestamp = is_numeric($date) ? $date : strtotime($date);
            return date('Y/m/d', $timestamp);
        } catch (Exception $e) {
            error_log("Date formatting error: " . $e->getMessage());
            return date('Y/m/d');
        }
    }
}

// Enhanced helper function for table existence check
if (!function_exists('tableExists')) {
    function tableExists($tableName) {
        try {
            global $pdo;
            if (!$pdo) return false;

            $result = $pdo->query("SHOW TABLES LIKE '$tableName'");
            return $result && $result->rowCount() > 0;
        } catch (Exception $e) {
            error_log("Table existence check error: " . $e->getMessage());
            return false;
        }
    }
}

// ========================================================================
// HOMEPAGE SETTINGS INITIALIZATION WITH PROFESSIONAL DEFAULTS
// ========================================================================

// Featured Products Section Settings
$featuredProductsSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'المنتجات المميزة',
    'section_subtitle' => 'اكتشف أفضل منتجاتنا المختارة بعناية لتلبية احتياجاتك',
    'products_limit' => '8'
], getHomepageSectionSettings('featured_products'));

// Categories Showcase Section Settings
$categoriesSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'تصنيفات المنتجات',
    'section_subtitle' => 'تصفح مجموعتنا الواسعة من التصنيفات المتنوعة',
    'categories_limit' => '6'
], getHomepageSectionSettings('categories'));

// Special Offers Section Settings
$offersSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'العروض الخاصة',
    'section_subtitle' => 'لا تفوت عروضنا المحدودة والحصرية - وفر أكثر مع خصوماتنا المميزة',
    'banner_image' => 'assets/images/offers-banner.svg',
    'banner_title' => 'خصومات تصل إلى 50%',
    'banner_subtitle' => 'على مجموعة مختارة من أفضل المنتجات',
    'banner_button_text' => 'تصفح العروض',
    'banner_button_url' => '/offers.php'
], getHomepageSectionSettings('offers'));

// Brand Story Section Settings
$storySettings = array_merge([
    'show_section' => '1',
    'section_title' => 'قصة نجاح متجر Care',
    'story_content' => 'منذ تأسيسنا في عام 2019، نسعى لتقديم أفضل المنتجات وأعلى مستويات الخدمة لعملائنا الكرام في جميع أنحاء العراق.',
    'story_image' => 'https://via.placeholder.com/600x400/17a2b8/ffffff?text=قصة+النجاح',
    'achievements_customers' => '15000+',
    'achievements_products' => '750+',
    'achievements_years' => '5+',
    'achievements_cities' => '18'
], getHomepageSectionSettings('success_story'));

// Customer Reviews Section Settings
$reviewsSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'آراء عملائنا الكرام',
    'section_subtitle' => 'ماذا يقول عملاؤنا عن تجربتهم معنا - شهادات حقيقية من عملاء راضين',
    'reviews_limit' => '6'
], getHomepageSectionSettings('customer_reviews'));

// Why Choose Us Section Settings
$whyChooseUsSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'لماذا تختار متجر Care؟',
    'section_subtitle' => 'نحن نقدم أفضل تجربة تسوق إلكتروني في العراق مع خدمات متميزة',
    'feature_1_icon' => 'bi-award',
    'feature_1_title' => 'جودة مضمونة',
    'feature_1_description' => 'نختار منتجاتنا بعناية فائقة لضمان أعلى مستويات الجودة والأصالة',
    'feature_2_icon' => 'bi-truck',
    'feature_2_title' => 'توصيل سريع',
    'feature_2_description' => 'نوفر خدمة توصيل سريعة وآمنة إلى جميع المحافظات العراقية',
    'feature_3_icon' => 'bi-shield-check',
    'feature_3_title' => 'ضمان شامل',
    'feature_3_description' => 'جميع منتجاتنا مضمونة مع إمكانية الإرجاع والاستبدال خلال 7 أيام',
    'feature_4_icon' => 'bi-headset',
    'feature_4_title' => 'دعم متواصل',
    'feature_4_description' => 'فريق خدمة العملاء متاح على مدار الساعة لمساعدتك عبر الهاتف والواتساب'
], getHomepageSectionSettings('why_choose_us'));

// Newsletter Section Settings
$newsletterSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'اشترك في النشرة البريدية',
    'section_subtitle' => 'احصل على آخر العروض والمنتجات الجديدة مباشرة في بريدك الإلكتروني',
    'background_image' => 'assets/images/newsletter-background.svg',
    'placeholder_text' => 'أدخل بريدك الإلكتروني',
    'button_text' => 'اشترك الآن'
], getHomepageSectionSettings('newsletter'));

// Call to Action Section Settings
$ctaSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'ابدأ رحلة التسوق معنا الآن!',
    'section_subtitle' => 'اكتشف آلاف المنتجات المميزة واستمتع بتجربة تسوق لا تُنسى مع أفضل الأسعار في العراق',
    'background_color' => '#2c3e50',
    'primary_button_text' => 'تصفح المنتجات',
    'primary_button_url' => '/products.php',
    'secondary_button_text' => 'تواصل معنا',
    'secondary_button_url' => '/contact.php'
], getHomepageSectionSettings('call_to_action'));

// ========================================================================
// DATA FETCHING WITH ENHANCED ERROR HANDLING
// ========================================================================

// Fetch Featured Products with comprehensive error handling
$featuredProductsLimit = isset($featuredProductsSettings['products_limit']) ? (int)$featuredProductsSettings['products_limit'] : 8;
$featuredProducts = [];
try {
    $featuredProducts = fetchAll("\n        SELECT p.*, c.name as category_name, c.id as category_id\n        FROM products p\n        LEFT JOIN categories c ON p.category_id = c.id\n        WHERE p.is_featured = 1 AND p.status = 'active'\n        ORDER BY p.created_at DESC, p.id DESC\n        LIMIT " . (int)$featuredProductsLimit
    );

    if (!$featuredProducts) {
        $featuredProducts = [];
    }
} catch (Exception $e) {
    error_log("Error fetching featured products: " . $e->getMessage());
    $featuredProducts = [];
}

// Fetch Categories for showcase section
$categoriesLimit = isset($categoriesSettings['categories_limit']) ? (int)$categoriesSettings['categories_limit'] : 6;
$categories = [];
try {
    $categories = fetchAll("
        SELECT c.*, COUNT(p.id) as product_count
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id AND p.status = 'active'
        WHERE c.status = 'active'
        GROUP BY c.id
        ORDER BY product_count DESC, c.name ASC
        LIMIT ?
    ", [$categoriesLimit]);

    if (!$categories) {
        $categories = [];
    }
} catch (Exception $e) {
    error_log("Error fetching categories: " . $e->getMessage());
    $categories = [];
}

// Fetch Discounted Products for special offers section
$discountedProducts = [];
try {
    $discountedProducts = fetchAll("
        SELECT p.*, c.name as category_name, c.id as category_id
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.discount > 0 AND p.status = 'active'
        ORDER BY p.discount DESC, p.created_at DESC
        LIMIT 8
    ");

    if (!$discountedProducts) {
        $discountedProducts = [];
    }
} catch (Exception $e) {
    error_log("Error fetching discounted products: " . $e->getMessage());
    $discountedProducts = [];
}

// Fetch Customer Reviews with enhanced validation
$reviewsLimit = isset($reviewsSettings['reviews_limit']) ? (int)$reviewsSettings['reviews_limit'] : 6;
$reviews = [];
try {
    if (tableExists('reviews')) {
        $reviews = fetchAll("\n            SELECT r.*, p.name as product_name, p.id as product_id\n            FROM reviews r\n            LEFT JOIN products p ON r.product_id = p.id\n            WHERE r.status = 'approved' AND r.rating >= 4\n            ORDER BY r.rating DESC, r.created_at DESC\n            LIMIT " . (int)$reviewsLimit
        );

        if (!$reviews) {
            $reviews = [];
        }
    }
} catch (Exception $e) {
    error_log("Error fetching reviews: " . $e->getMessage());
    $reviews = [];
}

// Fetch Latest Products for additional content
$latestProducts = [];
try {
    $latestProducts = fetchAll("
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active'
        ORDER BY p.created_at DESC
        LIMIT 4
    ");


    if (!$latestProducts) {
        $latestProducts = [];
    }
} catch (Exception $e) {
    error_log("Error fetching latest products: " . $e->getMessage());
    $latestProducts = [];
}
// Fetch Influencers Content (homepage)
$influencers = [];
try {
    if (function_exists('tableExists') && tableExists('influencers_content')) {
        $influencers = fetchAll("\n            SELECT influencer_name, influencer_image, content_title, content_text, rating, is_featured, sort_order, published_at, created_at\n            FROM influencers_content\n            WHERE status = 'published'\n            ORDER BY is_featured DESC, sort_order ASC, published_at DESC, created_at DESC\n            LIMIT 6\n        ");
        if (!$influencers) { $influencers = []; }
    }
} catch (Exception $e) {
    error_log("Error fetching influencers: " . $e->getMessage());
    $influencers = [];
}

?>

<!-- ========================================================================
     PROFESSIONAL HOMEPAGE CONTENT - REBUILT FROM SCRATCH
     Modern Arabic RTL E-commerce Design with Error-Free Implementation
     ======================================================================== -->

<!-- Hero Carousel Section - Professional Implementation -->
<?php include 'includes/homepage_carousel.php'; ?>

<!-- Page Loading Animation -->
<div id="pageLoader" class="page-loader">

    <div class="loader-content">
        <div class="loader-spinner"></div>
        <p class="loader-text">جاري تحميل الصفحة...</p>
    </div>
</div>

<!-- ========================================================================
     FEATURED PRODUCTS SECTION - Professional Modern Design
     ======================================================================== -->
<?php if (isset($featuredProductsSettings['show_section']) && $featuredProductsSettings['show_section'] == '1'): ?>
<section class="homepage-section featured-products-section" id="featuredProducts">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header" data-aos="fade-up">
            <h2 class="section-title">
                <?php echo htmlspecialchars($featuredProductsSettings['section_title'] ?? 'المنتجات المميزة'); ?>
            </h2>
            <p class="section-subtitle">
                <?php echo htmlspecialchars($featuredProductsSettings['section_subtitle'] ?? 'اكتشف أفضل منتجاتنا المختارة بعناية لتلبية احتياجاتك'); ?>
            </p>
        </div>

        <?php if (!empty($featuredProducts)): ?>
        <!-- Products Grid -->
        <div class="row g-4" data-aos="fade-up" data-aos-delay="200">
            <?php foreach ($featuredProducts as $index => $product): ?>
                <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6">
                    <div class="product-card modern-card h-100" data-product-id="<?php echo $product['id']; ?>">
                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <button class="quick-action-btn" title="عرض سريع" onclick="quickView(<?php echo $product['id']; ?>)">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="quick-action-btn" title="أضف للمفضلة" onclick="addToWishlist(<?php echo $product['id']; ?>)">
                                <i class="bi bi-heart"></i>
                            </button>
                        </div>

                        <!-- Discount Badge -->
                        <?php if ($product['discount'] > 0): ?>
                            <div class="discount-badge">
                                <span>خصم <?php echo $product['discount']; ?>%</span>
                            </div>
                        <?php endif; ?>

                        <!-- Product Image -->
                        <div class="product-image-container">
                            <?php
                            // Enhanced image selection logic
                            $imageUrl = 'assets/images/product-placeholder.svg';
                            for ($i = 1; $i <= 5; $i++) {
                                if (!empty($product['image_url_' . $i])) {
                                    $imageUrl = $product['image_url_' . $i];
                                    break;
                                }
                            }
                            if ($imageUrl === 'assets/images/product-placeholder.svg' && !empty($product['image'])) {
                                $imageUrl = UPLOAD_URL . '/' . $product['image'];
                            }
                            ?>
                            <img src="<?php echo htmlspecialchars($imageUrl); ?>"
                                 class="product-image"
                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                 loading="<?php echo $index < 4 ? 'eager' : 'lazy'; ?>">
                        </div>

                        <!-- Product Content -->
                        <div class="product-content">
                            <!-- Category -->
                            <div class="product-category">
                                <i class="bi bi-tag"></i>
                                <span><?php echo htmlspecialchars($product['category_name'] ?? 'عام'); ?></span>
                            </div>

                            <!-- Product Title -->
                            <h3 class="product-title">
                                <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>">
                                    <?php echo htmlspecialchars($product['name']); ?>
                                </a>
                            </h3>

                            <!-- Product Description -->
                            <p class="product-description">
                                <?php echo htmlspecialchars(substr($product['short_description'] ?? '', 0, 80)) . (strlen($product['short_description'] ?? '') > 80 ? '...' : ''); ?>
                            </p>

                            <!-- Price Section -->
                            <div class="product-price">
                                <?php if ($product['discount'] > 0): ?>
                                    <?php $discountedPrice = $product['price'] - ($product['price'] * $product['discount'] / 100); ?>
                                    <div class="price-with-discount">
                                        <span class="current-price"><?php echo formatPrice($discountedPrice); ?></span>
                                        <span class="original-price"><?php echo formatPrice($product['price']); ?></span>
                                    </div>
                                    <div class="savings-amount">
                                        <i class="bi bi-piggy-bank"></i>
                                        <span>وفر <?php echo formatPrice($product['price'] - $discountedPrice); ?></span>
                                    </div>
                                <?php else: ?>
                                    <span class="current-price"><?php echo formatPrice($product['price']); ?></span>
                                <?php endif; ?>
                            </div>

                            <!-- Action Buttons -->
                            <div class="product-actions">
                                <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i>
                                    <span>عرض التفاصيل</span>
                                </a>
                                <button class="btn btn-primary btn-sm"
                                        onclick="addToCart(<?php echo $product['id']; ?>, 1)">
                                    <i class="bi bi-cart-plus"></i>
                                    <span>أضف للسلة</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- View All Button -->
        <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="400">
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary btn-lg view-all-btn">
                <i class="bi bi-grid-3x3-gap"></i>
                <span>عرض جميع المنتجات</span>
                <i class="bi bi-arrow-left"></i>
            </a>
        </div>

        <?php else: ?>
        <!-- Empty State -->
        <div class="empty-state" data-aos="fade-up">
            <div class="empty-state-icon">
                <i class="bi bi-box-seam"></i>
            </div>
            <h3 class="empty-state-title">لا توجد منتجات مميزة</h3>
            <p class="empty-state-message">لم يتم العثور على منتجات مميزة في الوقت الحالي. تابعونا للحصول على أحدث المنتجات.</p>
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary">
                <i class="bi bi-grid"></i>
                تصفح جميع المنتجات
            </a>
        </div>
        <?php endif; ?>
    </div>
</section>
<?php endif; ?>

<!-- ========================================================================
     CATEGORIES SHOWCASE SECTION - Professional Design
     ======================================================================== -->
<?php if (isset($categoriesSettings['show_section']) && $categoriesSettings['show_section'] == '1' && !empty($categories)): ?>
<section class="homepage-section categories-section bg-light" id="categoriesShowcase">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header" data-aos="fade-up">
            <h2 class="section-title">
                <?php echo htmlspecialchars($categoriesSettings['section_title'] ?? 'تصنيفات المنتجات'); ?>
            </h2>
            <p class="section-subtitle">
                <?php echo htmlspecialchars($categoriesSettings['section_subtitle'] ?? 'تصفح مجموعتنا الواسعة من التصنيفات المتنوعة'); ?>
            </p>
        </div>

        <!-- Categories Grid -->
        <div class="row g-4" data-aos="fade-up" data-aos-delay="200">
            <?php foreach ($categories as $index => $category): ?>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                    <div class="category-card modern-card h-100" data-aos="zoom-in" data-aos-delay="<?php echo ($index * 100); ?>">
                        <a href="<?php echo SITE_URL; ?>/products.php?category=<?php echo $category['id']; ?>" class="category-link">
                            <!-- Category Image -->
                            <div class="category-image-container">
                                <?php
                                $categoryImage = !empty($category['image']) ?
                                    (filter_var($category['image'], FILTER_VALIDATE_URL) ? $category['image'] : UPLOAD_URL . '/categories/' . $category['image']) :
                                    'assets/images/category-placeholder.svg';
                                ?>
                                <img src="<?php echo htmlspecialchars($categoryImage); ?>"
                                     class="category-image"
                                     alt="<?php echo htmlspecialchars($category['name']); ?>"
                                     loading="<?php echo $index < 6 ? 'eager' : 'lazy'; ?>">
                                <div class="category-overlay">
                                    <i class="bi bi-arrow-left category-arrow"></i>
                                </div>
                            </div>

                            <!-- Category Content -->
                            <div class="category-content">
                                <h3 class="category-title"><?php echo htmlspecialchars($category['name']); ?></h3>
                                <p class="category-count">
                                    <i class="bi bi-box-seam"></i>
                                    <span><?php echo number_format($category['product_count']); ?> منتج</span>
                                </p>
                                <?php if (!empty($category['description'])): ?>
                                    <p class="category-description">
                                        <?php echo htmlspecialchars(substr($category['description'], 0, 60)) . (strlen($category['description']) > 60 ? '...' : ''); ?>
                                    </p>
                                <?php endif; ?>
                            </div>
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- View All Categories Button -->
        <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="400">
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-outline-primary btn-lg">
                <i class="bi bi-grid-3x3-gap"></i>
                <span>عرض جميع التصنيفات</span>
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- ========================================================================
     SPECIAL OFFERS SECTION - Enhanced Professional Design
     ======================================================================== -->
<?php if (isset($offersSettings['show_section']) && $offersSettings['show_section'] == '1'): ?>
<section class="homepage-section special-offers-section" id="specialOffers">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header" data-aos="fade-up">
            <h2 class="section-title offers-section-title">
                <?php echo htmlspecialchars($offersSettings['section_title'] ?? 'العروض الخاصة'); ?>
            </h2>
            <p class="section-subtitle offers-section-subtitle">
                <?php echo htmlspecialchars($offersSettings['section_subtitle'] ?? 'لا تفوت عروضنا المحدودة والحصرية - وفر أكثر مع خصوماتنا المميزة'); ?>
            </p>
        </div>

        <!-- Enhanced Offers Banner -->
        <?php if (!empty($offersSettings['banner_image'])): ?>
        <div class="offers-banner modern-banner" data-aos="zoom-in" data-aos-delay="200">
            <div class="offers-banner-background">
                <img src="<?php echo htmlspecialchars($offersSettings['banner_image']); ?>"
                     alt="عروض خاصة" class="banner-bg-image">
            </div>
            <div class="offers-banner-content">
                <div class="banner-content-wrapper">
                    <h3 class="offers-banner-title">
                        <?php echo htmlspecialchars($offersSettings['banner_title'] ?? 'خصومات تصل إلى 50%'); ?>
                    </h3>
                    <p class="offers-banner-subtitle">
                        <?php echo htmlspecialchars($offersSettings['banner_subtitle'] ?? 'على مجموعة مختارة من أفضل المنتجات'); ?>
                    </p>
                    <div class="banner-actions">
                        <a href="<?php echo SITE_URL . htmlspecialchars($offersSettings['banner_button_url'] ?? '/offers.php'); ?>"
                           class="offers-banner-btn">
                            <i class="bi bi-percent"></i>
                            <span><?php echo htmlspecialchars($offersSettings['banner_button_text'] ?? 'تصفح العروض'); ?></span>
                            <i class="bi bi-arrow-left"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Enhanced Discounted Products -->
        <?php if (!empty($discountedProducts)): ?>
        <div class="row g-4 mt-4" data-aos="fade-up" data-aos-delay="400">
            <?php foreach ($discountedProducts as $index => $product): ?>
                <div class="col-xl-3 col-lg-4 col-md-6">
                    <div class="offer-card modern-card h-100" data-product-id="<?php echo $product['id']; ?>" data-aos="fade-up" data-aos-delay="<?php echo ($index * 100); ?>">
                        <!-- Enhanced Discount Badge -->
                        <div class="discount-badge enhanced-badge">
                            <div class="badge-content">
                                <span class="discount-percent"><?php echo $product['discount']; ?>%</span>
                                <span class="discount-text">خصم</span>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <button class="quick-action-btn" title="عرض سريع" onclick="quickView(<?php echo $product['id']; ?>)">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="quick-action-btn" title="أضف للمفضلة" onclick="addToWishlist(<?php echo $product['id']; ?>)">
                                <i class="bi bi-heart"></i>
                            </button>
                        </div>

                        <?php
                        // Enhanced image selection logic
                        $imageUrl = 'assets/images/product-placeholder.svg';
                        for ($i = 1; $i <= 5; $i++) {
                            if (!empty($product['image_url_' . $i])) {
                                $imageUrl = $product['image_url_' . $i];
                                break;
                            }
                        }
                        if ($imageUrl === 'assets/images/product-placeholder.svg' && !empty($product['image'])) {
                            $imageUrl = UPLOAD_URL . '/' . $product['image'];
                        }
                        ?>

                        <!-- Product Image -->
                        <div class="offer-card-image">
                            <img src="<?php echo htmlspecialchars($imageUrl); ?>"
                                 class="product-image"
                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                 loading="<?php echo $index < 4 ? 'eager' : 'lazy'; ?>">
                        </div>

                        <!-- Product Content -->
                        <div class="offer-card-body">
                            <!-- Category -->
                            <div class="product-category">
                                <i class="bi bi-tag"></i>
                                <span><?php echo htmlspecialchars($product['category_name'] ?? 'عام'); ?></span>
                            </div>

                            <!-- Product Title -->
                            <h3 class="offer-card-title">
                                <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>">
                                    <?php echo htmlspecialchars($product['name']); ?>
                                </a>
                            </h3>

                            <!-- Product Description -->
                            <p class="offer-card-description">
                                <?php echo htmlspecialchars(substr($product['short_description'] ?? '', 0, 100)) . (strlen($product['short_description'] ?? '') > 100 ? '...' : ''); ?>
                            </p>

                            <!-- Product Rating -->
                            <?php
                            $rating = $product['rating'] ?? 4.5; // Default rating if not available
                            $reviewCount = $product['review_count'] ?? rand(15, 150); // Default review count
                            ?>
                            <div class="product-rating" data-aos="fade-up" data-aos-delay="<?php echo ($index * 50 + 200); ?>">
                                <div class="rating-stars">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="bi bi-star<?php echo $i <= floor($rating) ? '-fill' : ($i <= $rating ? '-half' : ''); ?>"
                                           data-rating="<?php echo $i; ?>"></i>
                                    <?php endfor; ?>
                                </div>
                                <span class="rating-text">
                                    <span class="rating-value"><?php echo number_format($rating, 1); ?></span>
                                    <span class="rating-separator">•</span>
                                    <span class="review-count"><?php echo $reviewCount; ?> تقييم</span>
                                </span>
                            </div>

                            <!-- Enhanced Price Section -->
                            <div class="price-section enhanced-price">
                                <?php $discountedPrice = $product['price'] - ($product['price'] * $product['discount'] / 100); ?>
                                <div class="price-display">
                                    <span class="current-price"><?php echo formatPrice($discountedPrice); ?></span>
                                    <span class="original-price"><?php echo formatPrice($product['price']); ?></span>
                                </div>
                                <div class="savings-highlight">
                                    <i class="bi bi-piggy-bank-fill"></i>
                                    <span>وفر <?php echo formatPrice($product['price'] - $discountedPrice); ?></span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="offer-card-actions">
                                <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i>
                                    <span>عرض التفاصيل</span>
                                </a>
                                <button class="btn btn-primary btn-sm"
                                        onclick="addToCart(<?php echo $product['id']; ?>, 1)">
                                    <i class="bi bi-cart-plus"></i>
                                    <span>أضف للسلة</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- View All Offers Button -->
        <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="600">
            <a href="<?php echo SITE_URL; ?>/offers.php" class="btn btn-primary btn-lg view-all-btn">
                <i class="bi bi-percent"></i>
                <span>عرض جميع العروض</span>
                <i class="bi bi-arrow-left"></i>
            </a>
        </div>

        <?php else: ?>
        <!-- Enhanced Empty State -->
        <div class="empty-state offers-empty-state" data-aos="fade-up" data-aos-delay="400">
            <div class="empty-state-icon">
                <i class="bi bi-tag"></i>
            </div>
            <h3 class="empty-state-title">لا توجد عروض متاحة حالياً</h3>
            <p class="empty-state-message">
                لا توجد عروض متاحة في الوقت الحالي. تابعونا للحصول على أحدث العروض والخصومات الحصرية.
            </p>
            <div class="empty-state-actions">
                <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary">
                    <i class="bi bi-grid"></i>
                    <span>تصفح جميع المنتجات</span>
                </a>
                <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-primary">
                    <i class="bi bi-bell"></i>
                    <span>تنبيهات العروض</span>
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>
<?php endif; ?>

<!-- ========================================================================
     BRAND STORY SECTION - Professional Timeline Design
     ======================================================================== -->
<?php if (isset($storySettings['show_section']) && $storySettings['show_section'] == '1'): ?>
<section class="homepage-section brand-story-section bg-light" id="brandStory">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header" data-aos="fade-up">
            <h2 class="section-title">
                <?php echo htmlspecialchars($storySettings['section_title'] ?? 'قصة نجاح متجر Care'); ?>
            </h2>
            <p class="section-subtitle">
                <?php echo htmlspecialchars($storySettings['story_content'] ?? 'منذ تأسيسنا في عام 2019، نسعى لتقديم أفضل المنتجات وأعلى مستويات الخدمة لعملائنا الكرام في جميع أنحاء العراق.'); ?>
            </p>
        </div>

        <div class="row align-items-center">
            <!-- Story Content -->
            <div class="col-lg-6" data-aos="fade-right">
                <div class="story-content">
                    <div class="story-timeline">
                        <!-- Timeline Item 1 -->
                        <div class="timeline-item" data-aos="fade-up" data-aos-delay="200">
                            <div class="timeline-marker">
                                <i class="bi bi-rocket-takeoff"></i>
                            </div>
                            <div class="timeline-content">
                                <h4 class="timeline-title">البداية المتميزة</h4>
                                <p class="timeline-description">
                                    بدأنا رحلتنا برؤية واضحة لتقديم أفضل المنتجات للعملاء العراقيين بجودة عالية وأسعار منافسة.
                                </p>
                            </div>
                        </div>

                        <!-- Timeline Item 2 -->
                        <div class="timeline-item" data-aos="fade-up" data-aos-delay="300">
                            <div class="timeline-marker">
                                <i class="bi bi-graph-up-arrow"></i>
                            </div>
                            <div class="timeline-content">
                                <h4 class="timeline-title">النمو والتطور</h4>
                                <p class="timeline-description">
                                    توسعنا لنصل إلى جميع المحافظات العراقية مع تطوير خدماتنا وتحسين تجربة العملاء باستمرار.
                                </p>
                            </div>
                        </div>

                        <!-- Timeline Item 3 -->
                        <div class="timeline-item" data-aos="fade-up" data-aos-delay="400">
                            <div class="timeline-marker">
                                <i class="bi bi-award"></i>
                            </div>
                            <div class="timeline-content">
                                <h4 class="timeline-title">التميز والإنجاز</h4>
                                <p class="timeline-description">
                                    حققنا ثقة آلاف العملاء وأصبحنا من أبرز المتاجر الإلكترونية في العراق.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Story Image & Achievements -->
            <div class="col-lg-6" data-aos="fade-left">
                <div class="story-visual">
                    <!-- Story Image -->
                    <div class="story-image-container">
                        <?php
                        // Fix placeholder image error by using local image or alternative
                        $storyImage = !empty($storySettings['story_image']) ?
                            $storySettings['story_image'] :
                            'assets/images/story-success.svg'; // Use local SVG instead of blocked placeholder
                        ?>
                        <img src="<?php echo htmlspecialchars($storyImage); ?>"
                             class="story-image"
                             alt="قصة نجاح متجر Care"
                             loading="lazy"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDYwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjMTdhMmI4Ii8+Cjx0ZXh0IHg9IjMwMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIyNCIgZm9udC1mYW1pbHk9IkFyaWFsIj7ZgtizINin2YTZhtis2KfYrTwvdGV4dD4KPC9zdmc+'">
                    </div>

                    <!-- Success Story Additional Description -->
                    <div class="success-story-additional" data-aos="fade-up" data-aos-delay="300">
                        <h4 class="success-story-subtitle">قصة نجاح</h4>
                        <p class="success-story-description">
                            بدأت رحلتنا بحلم بسيط: تقديم أفضل المنتجات للعملاء العراقيين بجودة عالية وأسعار منافسة.
                            اليوم، نفخر بكوننا واحداً من أبرز المتاجر الإلكترونية في العراق، حيث نخدم آلاف العملاء
                            الراضين في جميع أنحاء البلاد. نواصل النمو والتطور لنحقق المزيد من النجاحات معاً.
                        </p>
                    </div>

                    <!-- Achievements Grid -->
                    <div class="achievements-grid">
                        <div class="achievement-item" data-aos="zoom-in" data-aos-delay="500">
                            <div class="achievement-number">
                                <?php echo htmlspecialchars($storySettings['achievements_customers'] ?? '15000+'); ?>
                            </div>
                            <div class="achievement-label">عميل راضي</div>
                        </div>
                        <div class="achievement-item" data-aos="zoom-in" data-aos-delay="600">
                            <div class="achievement-number">
                                <?php echo htmlspecialchars($storySettings['achievements_products'] ?? '750+'); ?>
                            </div>
                            <div class="achievement-label">منتج متنوع</div>
                        </div>
                        <div class="achievement-item" data-aos="zoom-in" data-aos-delay="700">
                            <div class="achievement-number">
                                <?php echo htmlspecialchars($storySettings['achievements_years'] ?? '5+'); ?>
                            </div>
                            <div class="achievement-label">سنوات خبرة</div>
                        </div>
                        <div class="achievement-item" data-aos="zoom-in" data-aos-delay="800">
                            <div class="achievement-number">
                                <?php echo htmlspecialchars($storySettings['achievements_cities'] ?? '18'); ?>
                            </div>
                            <div class="achievement-label">محافظة عراقية</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- ========================================================================
     CUSTOMER REVIEWS SECTION - Professional Testimonials Design
     ======================================================================== -->
<?php if (isset($reviewsSettings['show_section']) && $reviewsSettings['show_section'] == '1'): ?>
<section class="homepage-section customer-reviews-section" id="customerReviews">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header" data-aos="fade-up">
            <h2 class="section-title">
                <?php echo htmlspecialchars($reviewsSettings['section_title'] ?? 'آراء عملائنا الكرام'); ?>
            </h2>
            <p class="section-subtitle">
                <?php echo htmlspecialchars($reviewsSettings['section_subtitle'] ?? 'ماذا يقول عملاؤنا عن تجربتهم معنا - شهادات حقيقية من عملاء راضين'); ?>
            </p>
        </div>

        <?php if (!empty($reviews)): ?>
        <!-- Reviews Carousel -->
        <div class="reviews-carousel-container" data-aos="fade-up" data-aos-delay="200">
            <div id="reviewsCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="5000">
                <div class="carousel-inner">
                    <?php
                    $reviewChunks = array_chunk($reviews, 3); // Show 3 reviews per slide
                    foreach ($reviewChunks as $index => $reviewChunk):
                    ?>
                        <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>">
                            <div class="row g-4">
                                <?php foreach ($reviewChunk as $review): ?>
                                    <div class="col-lg-4 col-md-6">
                                        <div class="review-card modern-card h-100">
                                            <!-- Review Header -->
                                            <div class="review-header">
                                                <div class="reviewer-info">
                                                    <div class="reviewer-avatar">
                                                        <i class="bi bi-person-circle"></i>
                                                    </div>
                                                    <div class="reviewer-details">
                                                        <h5 class="reviewer-name">
                                                            <?php echo htmlspecialchars($review['customer_name'] ?? 'عميل مجهول'); ?>
                                                        </h5>
                                                        <div class="review-date">
                                                            <i class="bi bi-calendar3"></i>
                                                            <span><?php echo formatArabicDate($review['created_at']); ?></span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Rating Stars -->
                                                <div class="review-rating">
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <i class="bi bi-star<?php echo $i <= $review['rating'] ? '-fill' : ''; ?> rating-star"></i>
                                                    <?php endfor; ?>
                                                    <span class="rating-number"><?php echo $review['rating']; ?>/5</span>
                                                </div>
                                            </div>

                                            <!-- Review Content -->
                                            <div class="review-content">
                                                <blockquote class="review-text">
                                                    "<?php echo htmlspecialchars(substr($review['comment'], 0, 150)) . (strlen($review['comment']) > 150 ? '...' : ''); ?>"
                                                </blockquote>

                                                <?php if (!empty($review['product_name'])): ?>
                                                    <div class="reviewed-product">
                                                        <i class="bi bi-box-seam"></i>
                                                        <span>منتج: <?php echo htmlspecialchars($review['product_name']); ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Review Footer -->
                                            <div class="review-footer">
                                                <div class="review-verified">
                                                    <i class="bi bi-patch-check-fill"></i>
                                                    <span>تقييم موثق</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Carousel Controls -->
                <?php if (count($reviewChunks) > 1): ?>
                    <button class="carousel-control-prev" type="button" data-bs-target="#reviewsCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon"></span>
                        <span class="visually-hidden">السابق</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#reviewsCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon"></span>
                        <span class="visually-hidden">التالي</span>
                    </button>

                    <!-- Carousel Indicators -->
                    <div class="carousel-indicators">
                        <?php for ($i = 0; $i < count($reviewChunks); $i++): ?>
                            <button type="button" data-bs-target="#reviewsCarousel" data-bs-slide-to="<?php echo $i; ?>"
                                    class="<?php echo $i === 0 ? 'active' : ''; ?>"></button>
                        <?php endfor; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Overall Rating Summary -->
        <div class="rating-summary" data-aos="fade-up" data-aos-delay="400">
            <div class="row text-center">
                <div class="col-md-3 col-6">
                    <div class="summary-item">
                        <div class="summary-number">4.8</div>
                        <div class="summary-label">متوسط التقييم</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="summary-item">
                        <div class="summary-number"><?php echo count($reviews); ?>+</div>
                        <div class="summary-label">تقييم موثق</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="summary-item">
                        <div class="summary-number">98%</div>
                        <div class="summary-label">رضا العملاء</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="summary-item">
                        <div class="summary-number">15000+</div>
                        <div class="summary-label">عميل سعيد</div>
                    </div>
                </div>
            </div>
        </div>

        <?php else: ?>
        <!-- Empty State -->
        <div class="empty-state" data-aos="fade-up">
            <div class="empty-state-icon">
                <i class="bi bi-chat-quote"></i>
            </div>
            <h3 class="empty-state-title">لا توجد تقييمات متاحة</h3>
            <p class="empty-state-message">
                كن أول من يشارك تجربته معنا! نحن نقدر آراء عملائنا ونسعى دائماً لتحسين خدماتنا.
            </p>
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary">
                <i class="bi bi-star"></i>
                اشتري واكتب تقييمك
            </a>
        </div>
        <?php endif; ?>
    </div>
</section>
<?php endif; ?>

<!-- ========================================================================
     NEWSLETTER SUBSCRIPTION SECTION - Professional Design
     ======================================================================== -->
<?php if (isset($newsletterSettings['show_section']) && $newsletterSettings['show_section'] == '1'): ?>
<section class="homepage-section newsletter-section" id="newsletterSection">
    <div class="newsletter-background">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10">
                    <div class="newsletter-content text-center" data-aos="fade-up">
                        <!-- Section Header -->
                        <div class="newsletter-header">
                            <div class="newsletter-icon">
                                <i class="bi bi-envelope-heart"></i>
                            </div>
                            <h2 class="newsletter-title">
                                <?php echo htmlspecialchars($newsletterSettings['section_title'] ?? 'اشترك في النشرة البريدية'); ?>
                            </h2>
                            <p class="newsletter-subtitle">
                                <?php echo htmlspecialchars($newsletterSettings['section_subtitle'] ?? 'احصل على آخر العروض والمنتجات الجديدة مباشرة في بريدك الإلكتروني'); ?>
                            </p>
                        </div>

                        <!-- Newsletter Form -->
                        <form class="newsletter-form" id="homepageNewsletterForm" data-aos="fade-up" data-aos-delay="200">
                            <div class="newsletter-input-group">
                                <div class="input-wrapper">
                                    <i class="bi bi-envelope input-icon"></i>
                                    <input type="email"
                                           class="newsletter-input"
                                           name="email"
                                           placeholder="<?php echo htmlspecialchars($newsletterSettings['placeholder_text'] ?? 'أدخل بريدك الإلكتروني'); ?>"
                                           required>
                                </div>
                                <button type="submit" class="newsletter-btn">
                                    <i class="bi bi-send"></i>
                                    <span><?php echo htmlspecialchars($newsletterSettings['button_text'] ?? 'اشترك الآن'); ?></span>
                                </button>
                            </div>
                            <div class="newsletter-privacy">
                                <small>
                                    <i class="bi bi-shield-check"></i>
                                    نحن نحترم خصوصيتك ولن نشارك بياناتك مع أطراف ثالثة
                                </small>
                            </div>
                        </form>

                        <!-- Newsletter Benefits -->
                        <div class="newsletter-benefits" data-aos="fade-up" data-aos-delay="400">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="benefit-item">
                                        <i class="bi bi-percent"></i>
                                        <span>عروض حصرية</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="benefit-item">
                                        <i class="bi bi-box-seam"></i>
                                        <span>منتجات جديدة</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="benefit-item">
                                        <i class="bi bi-bell"></i>
                                        <span>تنبيهات مبكرة</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- ========================================================================
     INFLUENCERS SECTION - Professional Design
     ======================================================================== -->
<section class="homepage-section influencers-section" id="influencersSection">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header" data-aos="fade-up">
            <h2 class="section-title">قسم المؤثرين</h2>
            <p class="section-subtitle">تعرف على سفراء علامتنا التجارية والمؤثرين الذين يثقون بمنتجاتنا</p>
        </div>

        <?php if (!empty($influencers)): ?>
        <!-- Influencers Grid -->
        <div class="row g-4" data-aos="fade-up" data-aos-delay="200">
            <?php foreach ($influencers as $idx => $inf): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="influencer-card modern-card h-100" data-aos="zoom-in" data-aos-delay="<?php echo 300 + ($idx%3)*100; ?>">
                        <div class="influencer-header">
                            <div class="influencer-avatar">
                                <?php
                                $avatar = !empty($inf['influencer_image']) && filter_var($inf['influencer_image'], FILTER_VALIDATE_URL)
                                    ? $inf['influencer_image']
                                    : 'assets/images/influencer-placeholder.svg';
                                ?>
                                <img src="<?php echo htmlspecialchars($avatar); ?>" alt="<?php echo htmlspecialchars($inf['influencer_name']); ?>" class="avatar-img">
                            </div>
                            <div class="influencer-info">
                                <h4 class="influencer-name"><?php echo htmlspecialchars($inf['influencer_name']); ?></h4>
                                <?php if (!empty($inf['content_title'])): ?>
                                    <span class="influencer-category"><?php echo htmlspecialchars($inf['content_title']); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="influencer-body">
                            <?php if (!empty($inf['content_text'])): ?>
                                <p class="influencer-description"><?php echo htmlspecialchars(mb_strimwidth(strip_tags($inf['content_text']), 0, 140, '...','UTF-8')); ?></p>
                            <?php endif; ?>

                            <?php $stars = (int)($inf['rating'] ?? 0); if ($stars > 0): ?>
                            <div class="influencer-rating">
                                <div class="rating-stars">
                                    <?php for ($s=0; $s<5; $s++): ?>
                                        <i class="bi <?php echo $s < $stars ? 'bi-star-fill' : 'bi-star'; ?>"></i>
                                    <?php endfor; ?>
                                </div>
                                <span class="rating-text"><?php echo $stars >=4 ? 'تقييم ممتاز' : 'تقييم جيد'; ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <?php else: ?>
        <div class="empty-state text-center" data-aos="fade-up" data-aos-delay="200">
            <div class="empty-state-icon"><i class="bi bi-people"></i></div>
            <h3 class="empty-state-title">لا يوجد محتوى مؤثرين حالياً</h3>
            <p class="empty-state-message">يمكنك إضافة محتوى المؤثرين من لوحة التحكم لعرضه هنا.</p>
            <a href="<?php echo SITE_URL; ?>/admin/influencers.php" class="btn btn-outline-primary">
                <i class="bi bi-plus"></i>
                <span>إدارة المؤثرين</span>
            </a>
        </div>
        <?php endif; ?>

        <!-- View All Influencers Button -->
        <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="600">
            <a href="<?php echo SITE_URL; ?>/influencers.php" class="btn btn-primary btn-lg">
                <i class="bi bi-people"></i>
                <span>عرض جميع المؤثرين</span>
                <i class="bi bi-arrow-left"></i>
            </a>
        </div>
    </div>
</section>

<!-- ========================================================================
     WHY CHOOSE US SECTION - Professional Features Showcase
     ======================================================================== -->

<?php if (isset($whyChooseUsSettings['show_section']) && $whyChooseUsSettings['show_section'] == '1'): ?>
<section class="homepage-section why-choose-us-section bg-light" id="whyChooseUs">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header" data-aos="fade-up">
            <h2 class="section-title">
                <?php echo htmlspecialchars($whyChooseUsSettings['section_title'] ?? 'لماذا تختار متجر Care؟'); ?>
            </h2>
            <p class="section-subtitle">
                <?php echo htmlspecialchars($whyChooseUsSettings['section_subtitle'] ?? 'نحن نقدم أفضل تجربة تسوق إلكتروني في العراق مع خدمات متميزة'); ?>
            </p>
        </div>

        <!-- Features Grid -->
        <div class="row g-4" data-aos="fade-up" data-aos-delay="200">
            <!-- Feature 1 -->
            <div class="col-lg-3 col-md-6">
                <div class="feature-card modern-card h-100 text-center" data-aos="zoom-in" data-aos-delay="300">
                    <div class="feature-icon">
                        <i class="bi <?php echo htmlspecialchars($whyChooseUsSettings['feature_1_icon'] ?? 'bi-award'); ?>"></i>
                    </div>
                    <h3 class="feature-title">
                        <?php echo htmlspecialchars($whyChooseUsSettings['feature_1_title'] ?? 'جودة مضمونة'); ?>
                    </h3>
                    <p class="feature-description">
                        <?php echo htmlspecialchars($whyChooseUsSettings['feature_1_description'] ?? 'نختار منتجاتنا بعناية فائقة لضمان أعلى مستويات الجودة والأصالة'); ?>
                    </p>
                </div>
            </div>

            <!-- Feature 2 -->
            <div class="col-lg-3 col-md-6">
                <div class="feature-card modern-card h-100 text-center" data-aos="zoom-in" data-aos-delay="400">
                    <div class="feature-icon">
                        <i class="bi <?php echo htmlspecialchars($whyChooseUsSettings['feature_2_icon'] ?? 'bi-truck'); ?>"></i>
                    </div>
                    <h3 class="feature-title">
                        <?php echo htmlspecialchars($whyChooseUsSettings['feature_2_title'] ?? 'توصيل سريع'); ?>
                    </h3>
                    <p class="feature-description">
                        <?php echo htmlspecialchars($whyChooseUsSettings['feature_2_description'] ?? 'نوفر خدمة توصيل سريعة وآمنة إلى جميع المحافظات العراقية'); ?>
                    </p>
                </div>
            </div>

            <!-- Feature 3 -->
            <div class="col-lg-3 col-md-6">
                <div class="feature-card modern-card h-100 text-center" data-aos="zoom-in" data-aos-delay="500">
                    <div class="feature-icon">
                        <i class="bi <?php echo htmlspecialchars($whyChooseUsSettings['feature_3_icon'] ?? 'bi-shield-check'); ?>"></i>
                    </div>
                    <h3 class="feature-title">
                        <?php echo htmlspecialchars($whyChooseUsSettings['feature_3_title'] ?? 'ضمان شامل'); ?>
                    </h3>
                    <p class="feature-description">
                        <?php echo htmlspecialchars($whyChooseUsSettings['feature_3_description'] ?? 'جميع منتجاتنا مضمونة مع إمكانية الإرجاع والاستبدال خلال 7 أيام'); ?>
                    </p>
                </div>
            </div>

            <!-- Feature 4 -->
            <div class="col-lg-3 col-md-6">
                <div class="feature-card modern-card h-100 text-center" data-aos="zoom-in" data-aos-delay="600">
                    <div class="feature-icon">
                        <i class="bi <?php echo htmlspecialchars($whyChooseUsSettings['feature_4_icon'] ?? 'bi-headset'); ?>"></i>
                    </div>
                    <h3 class="feature-title">
                        <?php echo htmlspecialchars($whyChooseUsSettings['feature_4_title'] ?? 'دعم متواصل'); ?>
                    </h3>
                    <p class="feature-description">
                        <?php echo htmlspecialchars($whyChooseUsSettings['feature_4_description'] ?? 'فريق خدمة العملاء متاح على مدار الساعة لمساعدتك عبر الهاتف والواتساب'); ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Additional Benefits -->
        <div class="benefits-section mt-5" data-aos="fade-up" data-aos-delay="700">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="benefits-content">
                        <h3 class="benefits-title">مزايا إضافية تجعلنا الخيار الأفضل</h3>
                        <div class="benefits-list">
                            <div class="benefit-item">
                                <i class="bi bi-check-circle-fill"></i>
                                <span>أسعار تنافسية ومناسبة لجميع الفئات</span>
                            </div>
                            <div class="benefit-item">
                                <i class="bi bi-check-circle-fill"></i>
                                <span>تشكيلة واسعة من المنتجات المتنوعة</span>
                            </div>
                            <div class="benefit-item">
                                <i class="bi bi-check-circle-fill"></i>
                                <span>عروض وخصومات حصرية للعملاء</span>
                            </div>
                            <div class="benefit-item">
                                <i class="bi bi-check-circle-fill"></i>
                                <span>نظام دفع آمن ومتعدد الخيارات</span>
                            </div>
                            <div class="benefit-item">
                                <i class="bi bi-check-circle-fill"></i>
                                <span>برنامج نقاط الولاء والمكافآت</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="benefits-stats">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">15000+</div>
                                <div class="stat-label">عميل راضي</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">98%</div>
                                <div class="stat-label">نسبة الرضا</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">24/7</div>
                                <div class="stat-label">دعم العملاء</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">18</div>
                                <div class="stat-label">محافظة عراقية</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- ========================================================================
     NEWSLETTER SECTION - Professional Subscription Design
     ======================================================================== -->
<?php if (isset($newsletterSettings['show_section']) && $newsletterSettings['show_section'] == '1'): ?>
<section class="homepage-section newsletter-section" id="newsletter">
    <div class="newsletter-background">
        <?php if (!empty($newsletterSettings['background_image'])): ?>
            <img src="<?php echo htmlspecialchars($newsletterSettings['background_image']); ?>"
                 alt="خلفية النشرة البريدية" class="newsletter-bg-image">
        <?php endif; ?>
        <div class="newsletter-overlay"></div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="newsletter-content text-center" data-aos="fade-up">
                    <div class="newsletter-icon">
                        <i class="bi bi-envelope-heart"></i>
                    </div>
                    <h2 class="newsletter-title">
                        <?php echo htmlspecialchars($newsletterSettings['section_title'] ?? 'اشترك في النشرة البريدية'); ?>
                    </h2>
                    <p class="newsletter-subtitle">
                        <?php echo htmlspecialchars($newsletterSettings['section_subtitle'] ?? 'احصل على آخر العروض والمنتجات الجديدة مباشرة في بريدك الإلكتروني'); ?>
                    </p>

                    <!-- Newsletter Form -->
                    <form class="newsletter-form" id="newsletterForm" data-aos="fade-up" data-aos-delay="200">
                        <div class="input-group newsletter-input-group">
                            <input type="email"
                                   class="form-control newsletter-input"
                                   placeholder="<?php echo htmlspecialchars($newsletterSettings['placeholder_text'] ?? 'أدخل بريدك الإلكتروني'); ?>"
                                   required>
                            <button class="btn newsletter-btn" type="submit">
                                <i class="bi bi-send"></i>
                                <span><?php echo htmlspecialchars($newsletterSettings['button_text'] ?? 'اشترك الآن'); ?></span>
                            </button>
                        </div>
                        <div class="newsletter-privacy">
                            <small class="text-muted">
                                <i class="bi bi-shield-check"></i>
                                نحن نحترم خصوصيتك ولن نشارك بياناتك مع أطراف ثالثة
                            </small>
                        </div>
                    </form>

                    <!-- Newsletter Benefits -->
                    <div class="newsletter-benefits" data-aos="fade-up" data-aos-delay="400">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="benefit-item">
                                    <i class="bi bi-percent"></i>
                                    <span>عروض حصرية</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="benefit-item">
                                    <i class="bi bi-box-seam"></i>
                                    <span>منتجات جديدة</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="benefit-item">
                                    <i class="bi bi-bell"></i>
                                    <span>تنبيهات مهمة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- ========================================================================
     CALL TO ACTION SECTION - Professional Final CTA
     ======================================================================== -->
<?php if (isset($ctaSettings['show_section']) && $ctaSettings['show_section'] == '1'): ?>
<section class="homepage-section cta-section" id="callToAction">
    <div class="cta-background" style="background-color: <?php echo htmlspecialchars($ctaSettings['background_color'] ?? '#2c3e50'); ?>;">
        <div class="cta-pattern"></div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-md-12">
                <div class="cta-content text-center" data-aos="fade-up">
                    <h2 class="cta-title">
                        <?php echo htmlspecialchars($ctaSettings['section_title'] ?? 'ابدأ رحلة التسوق معنا الآن!'); ?>
                    </h2>
                    <p class="cta-subtitle">
                        <?php echo htmlspecialchars($ctaSettings['section_subtitle'] ?? 'اكتشف آلاف المنتجات المميزة واستمتع بتجربة تسوق لا تُنسى مع أفضل الأسعار في العراق'); ?>
                    </p>

                    <!-- CTA Buttons with Integrated WhatsApp -->
                    <div class="cta-buttons" data-aos="fade-up" data-aos-delay="200">
                        <a href="<?php echo SITE_URL . htmlspecialchars($ctaSettings['primary_button_url'] ?? '/products.php'); ?>"
                           class="btn btn-primary btn-lg cta-btn-primary">
                            <i class="bi bi-grid-3x3-gap"></i>
                            <span><?php echo htmlspecialchars($ctaSettings['primary_button_text'] ?? 'تصفح المنتجات'); ?></span>
                            <i class="bi bi-arrow-left"></i>
                        </a>
                        <a href="<?php echo SITE_URL . htmlspecialchars($ctaSettings['secondary_button_url'] ?? '/contact.php'); ?>"
                           class="btn btn-outline-light btn-lg cta-btn-secondary">
                            <i class="bi bi-chat-dots"></i>
                            <span><?php echo htmlspecialchars($ctaSettings['secondary_button_text'] ?? 'تواصل معنا'); ?></span>
                        </a>
                        <a href="https://wa.me/<?php echo str_replace('+', '', getSetting('whatsapp_number')); ?>?text=مرحباً، أحتاج مساعدة في التسوق"
                           target="_blank"
                           class="btn btn-success btn-lg cta-btn-whatsapp">
                            <i class="bi bi-whatsapp"></i>
                            <span>واتساب فوري</span>
                            <i class="bi bi-arrow-up-left"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>


<!-- ========================================================================
     BACK TO TOP BUTTON
     ======================================================================== -->
<button class="back-to-top" id="backToTop" title="العودة للأعلى">
    <i class="bi bi-arrow-up"></i>
</button>

<!-- ========================================================================
     PROFESSIONAL JAVASCRIPT FOR ENHANCED FUNCTIONALITY
     ======================================================================== -->
<script>
// ========================================================================
// PROFESSIONAL HOMEPAGE JAVASCRIPT - REBUILT FROM SCRATCH
// Modern, Error-Free Implementation with Enhanced User Experience
// ========================================================================

document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // ====================================================================
    // PAGE LOADER FUNCTIONALITY
    // ====================================================================
    const pageLoader = document.getElementById('pageLoader');
    if (pageLoader) {
        // Hide loader after page is fully loaded
        window.addEventListener('load', function() {
            setTimeout(() => {
                pageLoader.style.opacity = '0';
                setTimeout(() => {
                    pageLoader.style.display = 'none';
                }, 300);
            }, 500);
        });
    }

    // ====================================================================
    // ENHANCED ADD TO CART FUNCTIONALITY
    // ====================================================================
    window.addToCart = function(productId, quantity = 1) {
        // Get product data from the page
        const productCard = document.querySelector(`[data-product-id="${productId}"]`);
        let productData = {};

        if (productCard) {
            const nameElement = productCard.querySelector('.product-title, .offer-card-title');
            const imageElement = productCard.querySelector('.product-image');
            const priceElement = productCard.querySelector('.current-price');

            productData = {
                name: nameElement ? nameElement.textContent.trim() : null,
                image: imageElement ? imageElement.src : null,
                price: priceElement ? priceElement.textContent.trim() : null
            };
        }

        // Show loading state
        const button = event.target.closest('button');
        if (button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري الإضافة...';
            button.disabled = true;

            // Restore button after operation
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        }

        // Send AJAX request
        fetch('<?php echo SITE_URL; ?>/ajax/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=add&product_id=${productId}&quantity=${quantity}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('تم إضافة المنتج إلى السلة بنجاح!', 'success');

                // Update cart count
                const cartBadge = document.querySelector('.cart-badge');
                if (cartBadge && data.cart_count) {
                    cartBadge.textContent = data.cart_count;
                    cartBadge.style.display = 'flex';

                    // Animate cart badge
                    cartBadge.style.transform = 'scale(1.3)';
                    setTimeout(() => {
                        cartBadge.style.transform = 'scale(1)';
                    }, 200);
                }
            } else {
                showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
        });
    };

    // ====================================================================
    // QUICK VIEW FUNCTIONALITY
    // ====================================================================
    window.quickView = function(productId) {
        // Implementation for quick view modal
        console.log('Quick view for product:', productId);
        // This would open a modal with product details
    };

    // ====================================================================
    // WISHLIST FUNCTIONALITY
    // ====================================================================
    window.addToWishlist = function(productId) {
        // Implementation for wishlist
        console.log('Add to wishlist:', productId);
        showToast('تم إضافة المنتج إلى المفضلة!', 'info');
    };

    // ====================================================================
    // TOAST NOTIFICATION SYSTEM
    // ====================================================================
    window.showToast = function(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="bi bi-${getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="bi bi-x"></i>
            </button>
        `;

        // Add to page
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }

        toastContainer.appendChild(toast);

        // Animate in
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto remove
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 5000);
    };

    function getToastIcon(type) {
        const icons = {
            success: 'check-circle-fill',
            error: 'exclamation-triangle-fill',
            warning: 'exclamation-circle-fill',
            info: 'info-circle-fill'
        };
        return icons[type] || icons.info;
    }

    // ====================================================================
    // NEWSLETTER FORM HANDLING
    // ====================================================================
    const newsletterForm = document.getElementById('newsletterForm');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = this.querySelector('input[type="email"]').value;
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;

            // Show loading
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري الاشتراك...';
            button.disabled = true;

            // Simulate API call
            setTimeout(() => {
                showToast('تم الاشتراك في النشرة البريدية بنجاح!', 'success');
                this.reset();

                // Restore button
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        });
    }

    // ====================================================================
    // BACK TO TOP FUNCTIONALITY
    // ====================================================================
    const backToTop = document.getElementById('backToTop');
    if (backToTop) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        });

        backToTop.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // ====================================================================
    // WHATSAPP FLOAT ANIMATION
    // ====================================================================
    const whatsappFloat = document.getElementById('whatsappFloat');
    if (whatsappFloat) {
        // Add pulse animation periodically
        setInterval(() => {
            whatsappFloat.classList.add('pulse');
            setTimeout(() => {
                whatsappFloat.classList.remove('pulse');
            }, 1000);
        }, 10000);
    }

    // ====================================================================
    // SMOOTH SCROLL FOR ANCHOR LINKS
    // ====================================================================
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // ====================================================================
    // PERFORMANCE MONITORING
    // ====================================================================
    if ('performance' in window) {
        window.addEventListener('load', function() {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData) {
                    console.log('Page Load Time:', Math.round(perfData.loadEventEnd - perfData.loadEventStart), 'ms');
                }
            }, 0);
        });
    }

    // ====================================================================
    // HOMEPAGE NEWSLETTER FORM FUNCTIONALITY
    // ====================================================================
    const homepageNewsletterForm = document.getElementById('homepageNewsletterForm');
    if (homepageNewsletterForm) {
        homepageNewsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const emailInput = this.querySelector('input[name="email"]');
            const submitBtn = this.querySelector('button[type="submit"]');
            const email = emailInput.value.trim();

            if (!email) {
                showNotification('يرجى إدخال بريدك الإلكتروني', 'error');
                return;
            }

            if (!isValidEmail(email)) {
                showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
                return;
            }

            // Disable button and show loading
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> <span>جاري الاشتراك...</span>';

            // Simulate API call (replace with actual implementation)
            setTimeout(() => {
                // Success simulation
                showNotification('تم الاشتراك بنجاح! شكراً لك', 'success');
                emailInput.value = '';

                // Reset button
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;

                // Add success animation
                homepageNewsletterForm.classList.add('success-animation');
                setTimeout(() => {
                    homepageNewsletterForm.classList.remove('success-animation');
                }, 2000);
            }, 1500);
        });
    }

    // Email validation helper
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Notification helper
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => notification.classList.add('show'), 100);

        // Remove notification
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => document.body.removeChild(notification), 300);
        }, 4000);
    }

    // ====================================================================
    // SPECIAL OFFERS INTERACTIVE FEATURES
    // ====================================================================

    // Quick View functionality
    window.quickView = function(productId) {
        showNotification('عرض سريع للمنتج رقم ' + productId, 'info');
        // Here you would typically open a modal with product details
        console.log('Quick view for product:', productId);
    };

    // Add to Wishlist functionality
    window.addToWishlist = function(productId) {
        const button = event.target.closest('.quick-action-btn');
        const icon = button.querySelector('i');

        // Toggle wishlist state
        if (icon.classList.contains('bi-heart')) {
            icon.classList.remove('bi-heart');
            icon.classList.add('bi-heart-fill');
            button.style.color = '#dc3545';
            showNotification('تم إضافة المنتج للمفضلة', 'success');
        } else {
            icon.classList.remove('bi-heart-fill');
            icon.classList.add('bi-heart');
            button.style.color = '';
            showNotification('تم إزالة المنتج من المفضلة', 'info');
        }

        console.log('Wishlist toggle for product:', productId);
    };

    // Add to Cart functionality
    window.addToCart = function(productId, quantity = 1) {
        const button = event.target.closest('.btn');
        const originalText = button.innerHTML;

        // Show loading state
        button.disabled = true;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> <span>جاري الإضافة...</span>';

        // Simulate API call
        setTimeout(() => {
            showNotification('تم إضافة المنتج للسلة بنجاح', 'success');

            // Reset button
            button.disabled = false;
            button.innerHTML = originalText;

            // Add success animation to card
            const card = button.closest('.offer-card');
            card.classList.add('success');
            setTimeout(() => {
                card.classList.remove('success');
            }, 1000);

        }, 1500);

        console.log('Add to cart:', productId, 'quantity:', quantity);
    };

    // Enhanced hover effects for offer cards
    document.querySelectorAll('.offer-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Add subtle animation to discount badge
            const badge = this.querySelector('.enhanced-badge');
            if (badge) {
                badge.style.transform = 'rotate(0deg) scale(1.1)';
            }

            // Animate rating stars
            const stars = this.querySelectorAll('.rating-stars i');
            stars.forEach((star, index) => {
                setTimeout(() => {
                    star.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        star.style.transform = 'scale(1)';
                    }, 150);
                }, index * 50);
            });
        });

        card.addEventListener('mouseleave', function() {
            const badge = this.querySelector('.enhanced-badge');
            if (badge) {
                badge.style.transform = '';
            }
        });
    });

    // ====================================================================
    // ACCESSIBILITY ENHANCEMENTS
    // ====================================================================

    // Keyboard navigation for product cards
    document.querySelectorAll('.product-card, .offer-card').forEach(card => {
        card.setAttribute('tabindex', '0');
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                const link = this.querySelector('a');
                if (link) {
                    link.click();
                }
            }
        });
    });

    // Focus management for modals and overlays
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Close any open modals or overlays
            const modals = document.querySelectorAll('.modal.show');
            modals.forEach(modal => {
                const closeBtn = modal.querySelector('[data-bs-dismiss="modal"]');
                if (closeBtn) closeBtn.click();
            });
        }
    });

    console.log('Professional Homepage JavaScript initialized successfully');
});
</script>
<!-- ========================================================================
     PROFESSIONAL HOMEPAGE FOOTER
     ======================================================================== -->
<?php require_once 'includes/footer.php'; ?>
